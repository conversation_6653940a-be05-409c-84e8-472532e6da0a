import { Injectable } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON>, PgBossJob<PERSON><PERSON><PERSON> } from '@wisemen/pgboss-nestjs-job'
import { NatsClient } from '../../nats.client.js'
import { NatsOutboxEvent, PublishNatsEventJob } from './publish-nats-event.job.js'

@Injectable()
@PgBossJobHandler(PublishNatsEventJob)
export class PublishNatsEventJobHandler extends JobHandler<PublishNatsEventJob> {
  constructor (
    private readonly natsClient: NatsClient
  ) {
    super()
  }

  public run (event: NatsOutboxEvent): void {
    this.natsClient.publish(event.subject, event.serializedMessage)
  }
}
