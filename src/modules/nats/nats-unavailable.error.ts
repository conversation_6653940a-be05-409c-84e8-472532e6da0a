import { ApiErrorCode } from '../exceptions/api-errors/api-error-code.decorator.js'
import { ServiceUnavailableApiError } from '../exceptions/api-errors/service-unavailable.api-error.js'

export class NatsUnavailableError extends ServiceUnavailableApiError {
  @ApiErrorCode('nats_unavailable')
  readonly code = 'nats_unavailable'

  meta: never

  constructor (detail: string) {
    super(detail)
  }
}
