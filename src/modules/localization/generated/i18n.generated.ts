/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "common": {
        "duration": {
            "hours": {
                "one": string;
                "other": string;
            };
        };
        "missing-translation": string;
    };
    "event-log": {
        "notification": {
            "created": {
                "v1": string;
            };
            "read": {
                "v1": string;
            };
            "unread": {
                "v1": string;
            };
            "types": {
                "migrated": {
                    "v1": string;
                };
            };
            "preference": {
                "preset": {
                    "updated": {
                        "v1": string;
                    };
                };
            };
        };
        "user": {
            "created": {
                "v1": string;
            };
            "role-assigned": {
                "v1": string;
            };
        };
        "contact": {
            "created": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
        };
        "role": {
            "permissions": {
                "updated": {
                    "v1": string;
                };
            };
            "created": {
                "v1": string;
            };
            "renamed": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
        };
    };
    "mail": {
        "password-reset": {
            "subject": string;
            "heading": string;
            "body": {
                "text": string;
                "subText": string;
            };
            "button": {
                "text": string;
            };
        };
    };
    "notification": {
        "user": {
            "created": string;
        };
        "test-notification": string;
    };
    "permissions": {
        "all_permissions": {
            "group-name": string;
            "name": string;
            "description": string;
        };
        "contact": {
            "group-name": string;
            "create": {
                "name": string;
                "description": string;
            };
            "read": {
                "name": string;
                "description": string;
            };
            "update": {
                "name": string;
                "description": string;
            };
            "delete": {
                "name": string;
                "description": string;
            };
        };
        "event-log": {
            "group-name": string;
            "read": {
                "name": string;
                "description": string;
            };
        };
        "file": {
            "group-name": string;
            "read": {
                "name": string;
                "description": string;
            };
            "create": {
                "name": string;
                "description": string;
            };
            "delete": {
                "name": string;
                "description": string;
            };
        };
        "jobs": {
            "group-name": string;
            "read": {
                "index": {
                    "name": string;
                    "description": string;
                };
                "detail": {
                    "name": string;
                    "description": string;
                };
            };
        };
        "notification": {
            "group-name": string;
            "read": {
                "own": {
                    "name": string;
                    "description": string;
                };
                "config": {
                    "name": string;
                    "description": string;
                };
            };
            "update": {
                "read": {
                    "name": string;
                    "description": string;
                };
                "unread": {
                    "name": string;
                    "description": string;
                };
            };
            "preferences": {
                "update": {
                    "channel": {
                        "name": string;
                        "description": string;
                    };
                    "preset": {
                        "name": string;
                        "description": string;
                    };
                    "types": {
                        "name": string;
                        "description": string;
                    };
                };
                "read": {
                    "own": {
                        "name": string;
                        "description": string;
                    };
                };
            };
            "migrate-type": {
                "name": string;
                "description": string;
            };
            "send-test": {
                "name": string;
                "description": string;
            };
        };
        "role": {
            "group-name": string;
            "read": {
                "name": string;
                "description": string;
            };
            "create": {
                "name": string;
                "description": string;
            };
            "update": {
                "name": string;
                "description": string;
            };
            "delete": {
                "name": string;
                "description": string;
            };
            "cache": {
                "clear": {
                    "name": string;
                    "description": string;
                };
            };
        };
        "send_push_notification": {
            "group-name": string;
            "name": string;
            "description": string;
        };
        "typesense": {
            "group-name": string;
            "name": string;
            "description": string;
        };
        "user": {
            "group-name": string;
            "read": {
                "name": string;
                "description": string;
            };
            "create": {
                "name": string;
                "description": string;
            };
            "update": {
                "name": string;
                "description": string;
            };
            "delete": {
                "name": string;
                "description": string;
            };
        };
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
