{"all_permissions": {"group-name": "Admin", "name": "All permissions", "description": "This permissions is a special permission which overrules all other permissions"}, "contact": {"group-name": "Contact", "create": {"name": "Create", "description": "Create contact persons"}, "read": {"name": "Read", "description": "View overview en detailed info about contact persons"}, "update": {"name": "Update", "description": "Update contact persons"}, "delete": {"name": "Delete", "description": "Delete contact persons"}}, "event-log": {"group-name": "Event Log", "read": {"name": "Read", "description": "View event logs"}}, "file": {"group-name": "Files", "read": {"name": "Read", "description": "View uploaded files"}, "create": {"name": "Create", "description": "Upload new files"}, "delete": {"name": "Delete", "description": "Remove uploaded files"}}, "jobs": {"group-name": "Jobs", "read": {"index": {"name": "View List", "description": "View job list"}, "detail": {"name": "View Details", "description": "View detailed job information"}}}, "notification": {"group-name": "Notifications", "read": {"own": {"name": "Read Own", "description": "Read own notifications"}, "config": {"name": "Read Configuration", "description": "Read notification configuration"}}, "update": {"read": {"name": "<PERSON>", "description": "Mark notifications as read"}, "unread": {"name": "<PERSON>", "description": "Mark notifications as unread"}}, "preferences": {"update": {"channel": {"name": "Update Channel Preferences", "description": "Change preferred notification channels"}, "preset": {"name": "Update Presets", "description": "Change notification preset settings"}, "types": {"name": "Update Types", "description": "Change allowed notification types"}}, "read": {"own": {"name": "Read Preferences", "description": "View your notification preferences"}}}, "migrate-type": {"name": "Migrate Type", "description": "Migrate notification types"}, "send-test": {"name": "Send Test Notification", "description": "Send a test notification"}}, "role": {"group-name": "Roles", "read": {"name": "Read", "description": "View roles and permissions"}, "create": {"name": "Create", "description": "Create new roles"}, "update": {"name": "Update", "description": "Update existing roles"}, "delete": {"name": "Delete", "description": "Delete roles"}, "cache": {"clear": {"name": "<PERSON>ache", "description": "Clear role permission cache"}}}, "send_push_notification": {"group-name": "Push Notifications", "name": "Send Push", "description": "Send push notifications to users"}, "typesense": {"group-name": "Search", "name": "Access Typesense", "description": "Access and manage Typesense search index"}, "user": {"group-name": "Users", "read": {"name": "Read", "description": "View user accounts"}, "create": {"name": "Create", "description": "Create user accounts"}, "update": {"name": "Update", "description": "Update user accounts"}, "delete": {"name": "Delete", "description": "Delete user accounts"}}}