import { ProductiveResponse } from './response.type.js'

export type TimeReportResponse = ProductiveResponse<TimeReportAttributes>

export interface TimeReportAttributes {
  currency: string
  currency_default: string
  currency_normalized: string
  group: string
  count: number
  formula_fields: unknown
  future: number
  billing_type?: unknown
  stage_type: number
  date_period?: string
  capacity: number
  available_time: number
  scheduled_time: number
  draft_scheduled_time: number
  scheduled_internal_time: number
  draft_scheduled_internal_time: number
  scheduled_client_time: number
  draft_scheduled_client_time: number
  scheduled_billable_time: number
  draft_scheduled_billable_time: number
  scheduled_event_time: number
  scheduled_remote_work_time: number
  internal_time: number
  client_time: number
  billable_time: number
  unapproved_time: number
  worked_time: number
  paid_event_time: number
  unpaid_event_time: number
  event_time: number
  workload: number
  recognized_scheduled_time: number
  recognized_time: number
  year: string
  quarter: string
  month: string
  week: string
  day: string
  average_cost_rate: number
  average_cost_rate_default: number
  average_cost_rate_normalized: number
  total_cost: number
  total_cost_default: number
  total_cost_normalized: number
  total_work_cost: number
  total_work_cost_default: number
  total_work_cost_normalized: number
  total_scheduled_revenue: number
  total_scheduled_revenue_default: number
  total_scheduled_revenue_normalized: number
  total_draft_scheduled_revenue: number
  total_draft_scheduled_revenue_default: number
  total_draft_scheduled_revenue_normalized: number
  total_scheduled_cost: number
  total_scheduled_cost_default: number
  total_scheduled_cost_normalized: number
  total_draft_scheduled_cost: number
  total_draft_scheduled_cost_default: number
  total_draft_scheduled_cost_normalized: number
  people_custom_fields: Peoplecustomfields
}

interface Peoplecustomfields {
  [key: string]: string
}
