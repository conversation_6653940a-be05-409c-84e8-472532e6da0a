export interface RelationShips {
  [key: string]: {
    data: {
      id: string
      type: string
    }
  } | {
    data: {
      id: string
      type: string
    }[]
  } | {
    meta: {
      included: boolean
    }
  }
  options: {
    data: {
      id: string
      type: string
    }[]
    meta: {
      included: boolean
    }
  }
  person: {
    data: {
      id: string
      type: string
    }
  }
  company: {
    data: {
      id: string
      type: string
    }
  }
}

export interface ProductiveData<T> {
  id: string
  type: string
  attributes: T
  relationships: RelationShips
}
