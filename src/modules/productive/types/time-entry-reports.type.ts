import { ProductiveResponse } from './response.type.js'

export type TimeEntryReportResponse = ProductiveResponse<TimeEntryReportAttributes>

export interface TimeEntryReportAttributes {
  currency?: string
  currency_default?: string
  currency_normalized?: string
  group?: string
  count?: number
  formula_fields?: unknown
  year?: string
  quarter?: string
  month?: string
  week?: string
  day?: string
  status?: unknown
  jira_issue_id?: unknown
  jira_issue_status?: unknown
  jira_issue_summary?: unknown
  invoiced?: boolean
  invoicing_status?: unknown
  overhead?: boolean
  section_name?: unknown
  total_time?: number
  total_billable_time?: number
  total_recognized_time?: number
  pricing_type_id?: unknown
  billing_type?: unknown
  unit_id?: unknown
  project_type_id?: unknown
  track_method_id?: unknown
  average_recognized_margin?: string
  autotracked?: unknown
  stage_type?: unknown
  intercompunknown_hours?: unknown
  people_custom_fields?: unknown
  custom_fields: unknown
  date_period?: string
  created_at_period?: unknown
  last_activity_at_period?: unknown
  started_at?: string
  ended_at?: string
  total_cost?: number
  total_cost_default?: number
  total_cost_normalized?: number
  average_blended_rate?: number
  average_blended_rate_default?: number
  average_blended_rate_normalized?: number
  total_recognized_revenue?: number
  total_recognized_revenue_default?: number
  total_recognized_revenue_normalized?: number
  total_recognized_profit?: number
  total_recognized_profit_default?: number
  total_recognized_profit_normalized?: number
  total_billable_revenue?: number
  total_billable_revenue_default?: number
  total_billable_revenue_normalized?: number
  avatar_url?: string
  contact?: unknown
  deactivated_at?: unknown
  email?: string
  first_name?: string
  last_name?: string
  nickname?: string
  original_avatar_url?: string
  role_id?: number
  status_emoji?: unknown
  status_expires_at?: unknown
  status_text?: unknown
  time_off_status_sync?: boolean
  title?: string
  archived_at?: string
  autotracking?: boolean
  joined_at?: string
  last_seen_at?: string
  invited_at?: string
  is_user?: boolean
  user_id?: number
  tag_list?: unknown[]
  virtual?: boolean
  created_at?: string
  placeholder?: boolean
  color_id?: unknown
  sample_data?: boolean
  time_unlocked?: boolean
  time_unlocked_on?: unknown
  time_unlocked_start_date?: unknown
  time_unlocked_end_date?: unknown
  time_unlocked_period_id?: unknown
  time_unlocked_interval?: unknown
  last_activity_at?: string
  two_factor_auth?: boolean
  availabilities?: string
  external_id?: string
  external_sync?: boolean
  hrm_type_id?: number
  champion?: boolean
  timesheet_submission_disabled?: boolean
}
