interface Settings {
  systemFlags: SystemFlags
  taxMandatoryCountries: SystemFlags
  taxIdsPerCountry: SystemFlags
}

interface Multigrouping {
  type: string
  available: boolean
}

interface SystemFlags {
  setting_type: string
  value: string
  updated_at: string
}

interface Customfields {
  type: string
  limit: number
}

interface Organizationfeatures {
  custom_fields: Customfields
  custom_fields_per_project: Customfields
  employee_fields: Customfields
  scheduling_placeholders: Customfields
  workflows: Customfields
  rate_cards: Customfields
  teams: Customfields
  outgoing_emails: Customfields
  recycle_bin: Customfields
  deal_stages: Customfields
  multiple_pipelines: Customfields
  forms: Customfields
  multigrouping: Multigrouping
  client_access_to_budgets: Multigrouping
  subsidiaries: Multigrouping
  time_approvals: Multigrouping
  expenses_approvals: Multigrouping
  timeoff_approvals: Multigrouping
  autotracking: Multigrouping
  forecasting: Multigrouping
  webhooks: Multigrouping
  table_pivoting: Multigrouping
  fiscal_year: Multigrouping
  formula_fields: Multigrouping
  single_sign_on: Multigrouping
  deals_billable_time_rounding: Multigrouping
  billable_time_rounding: Multigrouping
  payment_sync: Multigrouping
  import_tasks_csv: Multigrouping
  time_calendar_layout: Multigrouping
  remove_branding: Multigrouping
  custom_invoicing_email: Multigrouping
  google_calendar_layout: Multigrouping
  pulse: Multigrouping
  slack: Multigrouping
  task_custom_fields_library: Multigrouping
  comment_visibility: Multigrouping
  time_off_sync: Multigrouping
  time_locking: Multigrouping
  docs: Multigrouping
  scheduling_resource_utilization: Multigrouping
  enforce_two_factor_auth: Multigrouping
  personio_integration: Multigrouping
  task_dependencies: Multigrouping
  hris_integration: Multigrouping
  numbering_scheme: Multigrouping
  automations: Customfields
  dashboards_sharing: Multigrouping
  currency_picker: Multigrouping
  required_custom_fields: Multigrouping
  restricted_tracking: Multigrouping
  enforce_sso: Multigrouping
  jira_integration: Multigrouping
  docs_versions: Multigrouping
  zapier_integration: Multigrouping
  recurring_budgets: Multigrouping
  restricted_user_roles: Multigrouping
  service_tracking_toggle: Multigrouping
  repeating_tasks: Multigrouping
  save_public_and_private_view: Multigrouping
  alternating_work_hours: Multigrouping
  overhead_cost: Multigrouping
  show_only_filtered_data: Multigrouping
  invoicing_integrations: Multigrouping
  audit_log: Multigrouping
  hubspot_integration: Multigrouping
  booking_activity_modal: Multigrouping
  person_custom_field: Multigrouping
  template_center: Multigrouping
  custom_roles: Multigrouping
  purchase_orders: Multigrouping
  payment_reminders: Multigrouping
  gantt: Multigrouping
  tentative_bookings: Multigrouping
  task_view_sharing: Multigrouping
  view_sharing: Multigrouping
  org_chart: Multigrouping
  person_status: Multigrouping
  deal_cost_rates: Multigrouping
  custom_man_days: Multigrouping
  workload_view: Multigrouping
  rippling_integration: Multigrouping
  document_styler: Multigrouping
  tax_codebook: Multigrouping
  probability_per_stage: Multigrouping
  expense_sync: Multigrouping
  reports_sharing: Multigrouping
  service_custom_fields: Multigrouping
  sandbox: Multigrouping
  file_custom_field: Multigrouping
  financial_month_locking: Multigrouping
  timesheet_submission: Multigrouping
  time_off_approval_policies: Multigrouping
  tasks: Multigrouping
  projects: Multigrouping
  contacts: Multigrouping
  scenario_builder: Multigrouping
  scim: Multigrouping
  approval_policies: Multigrouping
  invoices_bulk_copy: Multigrouping
  email_inbox: Multigrouping
  deal_funnel_report: Multigrouping
  deals: Multigrouping
  time_tracking: Multigrouping
  expenses: Multigrouping
  invoices: Multigrouping
  payments: Multigrouping
  inbound_emails: Multigrouping
  time_offs: Multigrouping
  time_off_allocations: Multigrouping
  remote_work_absence: Multigrouping
  financials: Multigrouping
  document_templates: Multigrouping
  revenue_recognition: Multigrouping
  peppol_transactions: Customfields
  tracking_type: Multigrouping
}

export interface ProductiveMeta {
  current_page: number
  total_pages: number
  total_count: number
  page_size: number
  max_page_size: number
  organization_features: Organizationfeatures
  settings: Settings
}
