import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { AxiosInstance } from 'axios'
import dayjs, { Dayjs } from 'dayjs'
import { TimeReportResponse } from './types/time-reports.type.js'
import { CustomFieldsResponse } from './types/custom-fields.type.js'
import { PersonResponse } from './types/person.type.js'
import { TimeEntryReportResponse } from './types/time-entry-reports.type.js'
import { ProjectResponse } from './types/project.type.js'
import { BudgetAttributes, BudgetResponse } from './types/budget.type.js'
import { ProductiveData } from './types/data.type.js'

@Injectable()
export class ProductiveClient {
  private readonly client: AxiosInstance

  constructor (
    private readonly configService: ConfigService
  ) {
    this.client = axios.create({
      baseURL: 'https://api.productive.io/api',
      headers: {
        'X-Auth-Token': this.configService.getOrThrow<string>('PRODUCTIVE_API_KEY'),
        'X-Organization-Id': this.configService.getOrThrow<string>('PRODUCTIVE_ORG_ID'),
        'Content-Type': 'application/json'
      }
    })
  }

  public async fetchPeople (): Promise<PersonResponse> {
    const res = await this.client.get<PersonResponse>('/v2/people', {
      params: {
        // 'filter[status]': '1',
        'filter[person_type]': '1',
        'page[size]': 200
      }
    })

    return res.data
  }

  public async fetchCustomFields (): Promise<CustomFieldsResponse> {
    const res = await this.client.get<CustomFieldsResponse>('/v2/custom_fields', {
      params: {
        'archived': 'false',
        'global': 'true',
        'filter[customizable_type]': 'employees',
        'include': 'options'
      }
    })

    return res.data
  }

  public async fetchTimeReports (options: {
    group: Array<'person' | 'people_custom_fields[53997]' | 'date:week'>
    from?: Dayjs
    until?: Dayjs
    page?: number
  }): Promise<TimeReportResponse> {
    const from = options.from ?? dayjs()
    const until = options.until ?? dayjs().add(90, 'day')

    const res = await this.client.get<TimeReportResponse>('/v2/reports/time_reports', {
      params: {
        'filter[date][gt_eq]': from.format('YYYY-MM-DD'),
        'filter[date][lt_eq]': until.format('YYYY-MM-DD'),
        'include': 'person',
        'group': options.group.join(','),
        'page[size]': 200,
        'page[number]': options.page ?? 1
      }
    })

    return res.data
  }

  public async fetchTimeEntryReports (options: {
    group: Array<'person' | 'people_custom_fields[53997]' | 'date:week'>
    from?: Dayjs
    until?: Dayjs
    page?: number
  }): Promise<TimeEntryReportResponse> {
    const from = options.from ?? dayjs()
    const until = options.until ?? dayjs().add(90, 'day')

    const res = await this.client.get<TimeEntryReportResponse>('/v2/reports/time_entry_reports', {
      params: {
        'filter[date][gt_eq]': from.format('YYYY-MM-DD'),
        'filter[date][lt_eq]': until.format('YYYY-MM-DD'),
        'include': 'person',
        'group': options.group.join(','),
        'page[size]': 200,
        'page[number]': options.page ?? 1
      }
    })

    return res.data
  }

  public async fetchSupportEntryReports (options: {
    group: Array<'person' | 'people_custom_fields[53997]' | 'date:week'>
    from?: Dayjs
    until?: Dayjs
    page?: number
  }): Promise<TimeEntryReportResponse> {
    const from = options.from ?? dayjs()
    const until = options.until ?? dayjs().add(90, 'day')

    const res = await this.client.get<TimeEntryReportResponse>('/v2/reports/time_entry_reports', {
      params: {
        'filter[date][gt_eq]': from.format('YYYY-MM-DD'),
        'filter[date][lt_eq]': until.format('YYYY-MM-DD'),
        'include': 'person',
        'group': options.group.join(','),
        'filter[service.budget.custom_fields][75074][eq]': '256214,259631',
        'page[size]': 200,
        'page[number]': options.page ?? 1
      }
    })

    return res.data
  }

  public async fetchProjects (): Promise<ProjectResponse> {
    const res = await this.client.get<ProjectResponse>('/v2/projects', {
      params: {
        'filter[status]': '1',
        'filter[project_type]': '2',
        'filter[custom_fields][76053][contains]': '259155,259157',
        // 'filter[custom_fields][60377][not_eq][]': '197841',
        'filter[custom_fields][35119][any_of][]': '125594',
        // 'filter[$op]': 'and',
        'page[size]': 200,
        'include': 'company',
        'sort': 'name'
      }
    })

    return res.data
  }

  public async fetchSupportBudget (
    projectId: string,
    date: string
  ): Promise<ProductiveData<BudgetAttributes> | null> {
    const res = await this.client.get<BudgetResponse>('/v2/deals', {
      params: {
        'filter[project_id]': projectId,
        'filter[status]': '1',
        'filter[project_type]': '2',
        'filter[custom_fields[75074]][eq]': '256214,259631',
        'filter[date][lt_eq]': date,
        'filter[end_date][gt_eq]': date
      }
    })

    if (res.data.data.length === 0) {
      return null
    }

    return res.data.data[0]
  }
}
