import { Issue, LinearClient as LinearSdkClient } from '@linear/sdk'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'

@Injectable()
export class LinearClient {
  private readonly client: LinearSdkClient

  constructor (private readonly configService: ConfigService) {
    this.client = new LinearSdkClient({
      apiKey: this.configService.getOrThrow<string>('LINEAR_API_KEY')
    })
  }

  public async fetchOverdueIssues (): Promise<Issue[]> {
    const issues = await this.client.issues({
      filter: {
        state: { name: { neq: 'Done' } },
        dueDate: { lt: new Date() }
      }
    }).then(result => result.nodes)

    for (const issue of issues) {
      const project = await issue.project
      const assignee = await issue.assignee
      const labels = await issue.labels() // returns a connection
      const comments = await issue.comments() // returns a connection

      console.log(issue.title, project?.name, assignee?.name)
    }

    return issues
  }
}
