import { Issue, LinearClient as LinearSdkClient, User } from '@linear/sdk'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import dayjs from 'dayjs'

@Injectable()
export class LinearClient {
  private readonly client: LinearSdkClient

  constructor (private readonly configService: ConfigService) {
    this.client = new LinearSdkClient({
      apiKey: this.configService.getOrThrow<string>('LINEAR_API_KEY')
    })
  }

  public async fetchOverdueIssues (
    perPage: number = 50,
    after?: string
  ): Promise<{ issues: Issue[], hasNextPage: boolean, endCursor?: string }> {
    const result = await this.client.issues({
      filter: {
        state: { name: { in: ['Todo', 'In Progress'] } },
        dueDate: { lt: dayjs().add(1, 'day').toISOString() },
        assignee: { null: false },
        project: { null: false }
      },
      first: perPage,
      after
    })

    const issues = result.nodes

    return {
      issues,
      hasNextPage: result.pageInfo.hasNextPage,
      endCursor: result.pageInfo.endCursor
    }
  }

  public async fetchUsers (
    perPage: number = 100,
    after?: string
  ): Promise<{ users: User[], hasNextPage: boolean, endCursor?: string }> {
    const result = await this.client.users({ first: perPage, after })

    return {
      users: result.nodes,
      hasNextPage: result.pageInfo.hasNextPage,
      endCursor: result.pageInfo.endCursor
    }
  }
}
