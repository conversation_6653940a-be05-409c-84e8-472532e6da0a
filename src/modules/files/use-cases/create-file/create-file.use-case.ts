import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { SECONDS_PER_MINUTE } from '@wisemen/time'
import { File } from '../../entities/file.entity.js'
import { S3 } from '../../../s3/s3.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { FileEntityBuilder } from '../../entities/file-entity.builder.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { CreateFileCommand } from './create-file.command.js'
import { CreateFileResponse } from './create-file.response.js'
import { FileCreatedEvent } from './file-created.event.js'

@Injectable()
export class CreateFileUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(File)
    private fileRepository: Repository<File>,
    private readonly s3: S3,
    private readonly authContext: AuthContext
  ) {}

  async execute (command: CreateFileCommand): Promise<CreateFileResponse> {
    const file = new FileEntityBuilder()
      .withName(command.name)
      .withMimeType(command.mimeType)
      .withUploaderUuid(this.authContext.getUserUuid())
      .build()

    const expiresInTenMinutes = 10 * SECONDS_PER_MINUTE
    const uploadUrl = await this.s3.createTemporaryUploadUrl(file, expiresInTenMinutes)

    await transaction(this.dataSource, async () => {
      await this.fileRepository.insert(file)
      await this.eventEmitter.emitOne(new FileCreatedEvent(file))
    })

    return new CreateFileResponse(file, uploadUrl)
  }
}
