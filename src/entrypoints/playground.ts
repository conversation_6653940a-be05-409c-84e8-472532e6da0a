import '../modules/exceptions/sentry.js'

import { INestApplicationContext, Module } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { JobContainer } from '@wisemen/app-container'
import { SentryModule } from '@sentry/nestjs/setup'
import { Trace } from '@wisemen/opentelemetry'
import { ExceptionModule } from '../modules/exceptions/exception.module.js'
import { DefaultConfigModule } from '../modules/config/default-config.module.js'
import { DefaultTypeOrmModule } from '../modules/typeorm/default-typeorm.module.js'
import { LinearModule } from '../modules/linear/linear.module.js'
import { LinearClient } from '../modules/linear/linear.client.js'

@Module({
  imports: [
    SentryModule.forRoot(),
    DefaultConfigModule,
    DefaultTypeOrmModule.forRootAsync({ migrationsRun: false }),
    ExceptionModule,
    LinearModule
  ]
})
class PlayGroundModule {

}

export class Playground extends JobContainer {
  async bootstrap (): Promise<INestApplicationContext> {
    return await NestFactory.createApplicationContext(PlayGroundModule)
  }

  @Trace()
  async execute (app: INestApplicationContext): Promise<void> {
    const linearClient = app.get(LinearClient)
    await linearClient.fetchOverdueIssues()
  }
}

const _playground = new Playground()
