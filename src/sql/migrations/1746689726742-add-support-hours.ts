import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddSupportHours1746689726742 implements MigrationInterface {
  name = 'AddSupportHours1746689726742'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "person_time" ADD "total_support_time" integer NOT NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE "person_time" ADD "total_support_billable_time" integer NOT NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE "person_time" ADD "total_support_recognized_time" integer NOT NULL DEFAULT '0'`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "person_time" DROP COLUMN "total_support_recognized_time"`)
    await queryRunner.query(`ALTER TABLE "person_time" DROP COLUMN "total_support_billable_time"`)
    await queryRunner.query(`ALTER TABLE "person_time" DROP COLUMN "total_support_time"`)
  }
}
