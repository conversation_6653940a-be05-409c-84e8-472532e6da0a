import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddArchivedAt1745851196486 implements MigrationInterface {
  name = 'AddArchivedAt1745851196486'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "person_time" DROP CONSTRAINT "FK_9a421ae9a79c9336d03777cf2fd"`)
    await queryRunner.query(`ALTER TABLE "person" ADD "archived_at" TIMESTAMP`)
    await queryRunner.query(`ALTER TABLE "person_time" ADD CONSTRAINT "FK_f037534ddec6538aebce1141916" FOREIGN KEY ("person_id") REFERENCES "person"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "person_time" DROP CONSTRAINT "FK_f037534ddec6538aebce1141916"`)
    await queryRunner.query(`ALTER TABLE "person" DROP COLUMN "archived_at"`)
    await queryRunner.query(`ALTER TABLE "person_time" ADD CONSTRAINT "FK_9a421ae9a79c9336d03777cf2fd" FOREIGN KEY ("person_id") REFERENCES "person"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }
}
