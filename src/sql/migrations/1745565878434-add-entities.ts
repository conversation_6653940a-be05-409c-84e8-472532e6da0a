import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddEntities1745565878434 implements MigrationInterface {
  name = 'AddEntities1745565878434'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "planned_ahead" ("person_id" character varying NOT NULL, "date" date NOT NULL, "available_time" double precision NOT NULL, "scheduled_time" double precision NOT NULL, CONSTRAINT "PK_79104fda32bed9716bf6792c9fd" PRIMARY KEY ("person_id", "date"))`)
    await queryRunner.query(`CREATE TABLE "person" ("id" character varying NOT NULL, "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, "email" character varying NOT NULL, CONSTRAINT "PK_5fdaf670315c4b7e70cce85daa3" PRIMARY KEY ("id"))`)
    await queryRunner.query(`CREATE TABLE "custom_field_option" ("id" character varying NOT NULL, "name" character varying NOT NULL, "custom_field_id" character varying NOT NULL, CONSTRAINT "PK_ee182b18ccc5d5e1a8d500b9be3" PRIMARY KEY ("id"))`)
    await queryRunner.query(`CREATE TABLE "custom_field" ("id" character varying NOT NULL, "name" character varying NOT NULL, "data_type_id" integer NOT NULL, CONSTRAINT "PK_70c7eb2dfb5b81c051a6ba3ace8" PRIMARY KEY ("id"))`)
    await queryRunner.query(`CREATE TABLE "person_custom_field" ("person_id" character varying NOT NULL, "custom_field_id" character varying NOT NULL, "custom_field_option_id" character varying NOT NULL, CONSTRAINT "PK_60120420dd0ecf6aea0f5ee8f0d" PRIMARY KEY ("person_id", "custom_field_id", "custom_field_option_id"))`)
    await queryRunner.query(`CREATE TABLE "person_time" ("person_id" character varying NOT NULL, "week" character varying NOT NULL, "capacity" integer NOT NULL, "available_time" integer NOT NULL, "total_time" integer NOT NULL, "total_billable_time" integer NOT NULL, "total_recognized_time" integer NOT NULL, CONSTRAINT "PK_3a57afca502c426e33e16d9b57e" PRIMARY KEY ("person_id", "week"))`)
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" RENAME TO "notification_type_enum_old"`)
    await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('user.created', 'test-notification')`)
    await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum" USING "type"::"text"::"public"."notification_type_enum"`)
    await queryRunner.query(`DROP TYPE "public"."notification_type_enum_old"`)
    await queryRunner.query(`ALTER TYPE "public"."notification_migration_type_enum" RENAME TO "notification_migration_type_enum_old"`)
    await queryRunner.query(`CREATE TYPE "public"."notification_migration_type_enum" AS ENUM('user.created', 'test-notification')`)
    await queryRunner.query(`ALTER TABLE "notification_migration" ALTER COLUMN "type" TYPE "public"."notification_migration_type_enum" USING "type"::"text"::"public"."notification_migration_type_enum"`)
    await queryRunner.query(`DROP TYPE "public"."notification_migration_type_enum_old"`)
    await queryRunner.query(`ALTER TYPE "public"."notification_preferences_types_enum" RENAME TO "notification_preferences_types_enum_old"`)
    await queryRunner.query(`CREATE TYPE "public"."notification_preferences_types_enum" AS ENUM('user.created', 'test-notification')`)
    await queryRunner.query(`ALTER TABLE "notification_preferences" ALTER COLUMN "types" TYPE "public"."notification_preferences_types_enum"[] USING "types"::"text"::"public"."notification_preferences_types_enum"[]`)
    await queryRunner.query(`DROP TYPE "public"."notification_preferences_types_enum_old"`)
    await queryRunner.query(`ALTER TABLE "custom_field_option" ADD CONSTRAINT "FK_7553afa233e107135a89bf741ba" FOREIGN KEY ("custom_field_id") REFERENCES "custom_field"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "person_custom_field" ADD CONSTRAINT "FK_fb0ca47b87a311491b5cdb66c55" FOREIGN KEY ("person_id") REFERENCES "person"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "person_custom_field" ADD CONSTRAINT "FK_c9b662cd9664915153028eb5461" FOREIGN KEY ("custom_field_id") REFERENCES "custom_field"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "person_custom_field" ADD CONSTRAINT "FK_aeb9e29603624799abc87913894" FOREIGN KEY ("custom_field_option_id") REFERENCES "custom_field_option"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "person_time" ADD CONSTRAINT "FK_9a421ae9a79c9336d03777cf2fd" FOREIGN KEY ("person_id") REFERENCES "person"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "person_time" DROP CONSTRAINT "FK_9a421ae9a79c9336d03777cf2fd"`)
    await queryRunner.query(`ALTER TABLE "person_custom_field" DROP CONSTRAINT "FK_aeb9e29603624799abc87913894"`)
    await queryRunner.query(`ALTER TABLE "person_custom_field" DROP CONSTRAINT "FK_c9b662cd9664915153028eb5461"`)
    await queryRunner.query(`ALTER TABLE "person_custom_field" DROP CONSTRAINT "FK_fb0ca47b87a311491b5cdb66c55"`)
    await queryRunner.query(`ALTER TABLE "custom_field_option" DROP CONSTRAINT "FK_7553afa233e107135a89bf741ba"`)
    await queryRunner.query(`CREATE TYPE "public"."notification_preferences_types_enum_old" AS ENUM('user.created')`)
    await queryRunner.query(`ALTER TABLE "notification_preferences" ALTER COLUMN "types" TYPE "public"."notification_preferences_types_enum_old"[] USING "types"::"text"::"public"."notification_preferences_types_enum_old"[]`)
    await queryRunner.query(`DROP TYPE "public"."notification_preferences_types_enum"`)
    await queryRunner.query(`ALTER TYPE "public"."notification_preferences_types_enum_old" RENAME TO "notification_preferences_types_enum"`)
    await queryRunner.query(`CREATE TYPE "public"."notification_migration_type_enum_old" AS ENUM('user.created')`)
    await queryRunner.query(`ALTER TABLE "notification_migration" ALTER COLUMN "type" TYPE "public"."notification_migration_type_enum_old" USING "type"::"text"::"public"."notification_migration_type_enum_old"`)
    await queryRunner.query(`DROP TYPE "public"."notification_migration_type_enum"`)
    await queryRunner.query(`ALTER TYPE "public"."notification_migration_type_enum_old" RENAME TO "notification_migration_type_enum"`)
    await queryRunner.query(`CREATE TYPE "public"."notification_type_enum_old" AS ENUM('user.created')`)
    await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum_old" USING "type"::"text"::"public"."notification_type_enum_old"`)
    await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`)
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum_old" RENAME TO "notification_type_enum"`)
    await queryRunner.query(`DROP TABLE "person_time"`)
    await queryRunner.query(`DROP TABLE "person_custom_field"`)
    await queryRunner.query(`DROP TABLE "custom_field"`)
    await queryRunner.query(`DROP TABLE "custom_field_option"`)
    await queryRunner.query(`DROP TABLE "person"`)
    await queryRunner.query(`DROP TABLE "planned_ahead"`)
  }
}
