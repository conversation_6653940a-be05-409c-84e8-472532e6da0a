import { MigrationInterface, QueryRunner } from 'typeorm'

export class Add30Days1752744187403 implements MigrationInterface {
  name = 'Add30Days1752744187403'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "planned_ahead" RENAME COLUMN "available_time" TO "available_time90_days"`)
    await queryRunner.query(`ALTER TABLE "planned_ahead" RENAME COLUMN "scheduled_time" TO "scheduled_time90_days"`)
    await queryRunner.query(`ALTER TABLE "planned_ahead" ADD "available_time30_days" double precision NOT NULL DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "planned_ahead" ADD "scheduled_time30_days" double precision NOT NULL DEFAULT 0`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "planned_ahead" DROP COLUMN "scheduled_time30_days"`)
    await queryRunner.query(`ALTER TABLE "planned_ahead" DROP COLUMN "available_time30_days"`)
    await queryRunner.query(`ALTER TABLE "planned_ahead" RENAME COLUMN "scheduled_time90_days" TO "scheduled_time"`)
    await queryRunner.query(`ALTER TABLE "planned_ahead" RENAME COLUMN "available_time90_days" TO "available_time"`)
  }
}
