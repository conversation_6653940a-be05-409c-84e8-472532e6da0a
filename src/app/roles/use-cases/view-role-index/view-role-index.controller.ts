import { Controller, Get } from '@nestjs/common'
import { <PERSON>piT<PERSON>s, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { ViewRoleIndexResponse } from './view-role-index.response.js'
import { ViewRoleIndexUseCase } from './view-role-index.use-case.js'

@ApiTags('Role')
@Controller('roles')
@ApiOAuth2([])
export class ViewRoleIndexController {
  constructor (
    private readonly useCase: ViewRoleIndexUseCase
  ) {}

  @Get()
  @ApiOkResponse({
    description: 'The roles has been successfully received.',
    type: ViewRoleIndexResponse
  })
  @Permissions(Permission.ROLE_READ)
  async getRoles (): Promise<ViewRoleIndexResponse> {
    return await this.useCase.execute()
  }
}
