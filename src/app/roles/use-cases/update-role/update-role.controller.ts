import { Body, Controller, Post } from '@nestjs/common'
import { ApiTags, ApiOAuth2 } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { RoleUuid } from '../../entities/role.uuid.js'
import { UpdateRoleUseCase } from './update-role.use-case.js'
import { UpdateRoleCommand } from './update-role.command.js'

@ApiTags('Role')
@Controller('roles/:role')
@ApiOAuth2([])
export class UpdateRoleController {
  constructor (
    private readonly useCase: UpdateRoleUseCase
  ) {}

  @Post()
  @Permissions(Permission.ROLE_UPDATE)
  async updateRole (
    @Body() updateRoleCommand: UpdateRoleCommand,
    @UuidParam('role') uuid: RoleUuid
  ): Promise<void> {
    await this.useCase.execute(uuid, updateRoleCommand)
  }
}
