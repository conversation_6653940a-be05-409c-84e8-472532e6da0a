import { Column, Entity, OneToMany, PrimaryColumn, Relation } from 'typeorm'
import { CustomFieldOption } from './custom-field-option.entity.js'

@Entity()
export class CustomField {
  @PrimaryColumn({ type: 'varchar' })
  id: string

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'int' })
  dataTypeId: number

  @OneToMany(() => CustomFieldOption, options => options.customField)
  options: Relation<CustomFieldOption[]>
}
