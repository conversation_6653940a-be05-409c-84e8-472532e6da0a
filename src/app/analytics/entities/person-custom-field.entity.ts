import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Relation } from 'typeorm'
import { Person } from './person.entity.js'
import { CustomField } from './custom-field.entity.js'
import { CustomFieldOption } from './custom-field-option.entity.js'

@Entity()
export class PersonCustomField {
  @Column({ type: 'varchar', primary: true })
  personId: string

  @Column({ type: 'varchar', primary: true })
  customFieldId: string

  @Column({ type: 'varchar', primary: true })
  customFieldOptionId: string

  @ManyToOne(() => Person, person => person.customFields)
  @JoinColumn({ name: 'person_id' })
  person: Relation<Person>

  @ManyToOne(() => CustomField)
  @JoinColumn({ name: 'custom_field_id' })
  customField: Relation<CustomField>

  @ManyToOne(() => CustomFieldOption)
  @JoinColumn({ name: 'custom_field_option_id' })
  customFieldOptions: Relation<CustomField>
}
