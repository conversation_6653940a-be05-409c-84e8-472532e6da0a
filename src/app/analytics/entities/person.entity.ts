import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryColumn, Relation } from 'typeorm'
import { PersonCustomField } from './person-custom-field.entity.js'

@Entity()
export class Person {
  @PrimaryColumn({ type: 'varchar' })
  id: string

  @Column({ type: 'timestamp', nullable: true })
  archivedAt: Date | null

  @Column({ type: 'varchar' })
  firstName: string

  @Column({ type: 'varchar' })
  lastName: string

  @Column({ type: 'varchar' })
  email: string

  @OneToMany(() => PersonCustomField, customField => customField.person)
  customFields: Relation<PersonCustomField>[]
}
