import { Column, Entity } from 'typeorm'

@Entity()
export class PlannedAhead {
  @Column({ type: 'varchar', primary: true })
  personId: string

  @Column({ type: 'date', primary: true })
  date: string

  @Column({ type: 'float' })
  availableTime90Days: number

  @Column({ type: 'float' })
  scheduledTime90Days: number

  @Column({ type: 'float', default: 0 })
  availableTime30Days: number

  @Column({ type: 'float', default: 0 })
  scheduledTime30Days: number
}
