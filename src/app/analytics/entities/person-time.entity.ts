import { Colum<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToOne, PrimaryColumn, Relation } from 'typeorm'
import { Person } from './person.entity.js'

@Entity()
export class PersonTime {
  @PrimaryColumn({ type: 'varchar' })
  personId: string

  @PrimaryColumn({ type: 'varchar' })
  week: string

  @Column({ type: 'int' })
  capacity: number // time report

  @Column({ type: 'int' })
  availableTime: number // time report

  @Column({ type: 'int' })
  totalTime: number // time entry report

  @Column({ type: 'int' })
  totalBillableTime: number // time entry report

  @Column({ type: 'int' })
  totalRecognizedTime: number // time entry report

  @Column({ type: 'int', default: 0 })
  totalSupportTime: number // time entry report

  @Column({ type: 'int', default: 0 })
  totalSupportBillableTime: number // time entry report

  @Column({ type: 'int', default: 0 })
  totalSupportRecognizedTime: number // time entry report

  @ManyToOne(() => Person)
  @JoinColumn({ name: 'person_id' })
  person: Relation<Person>
}
