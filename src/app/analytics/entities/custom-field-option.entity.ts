import { Column, <PERSON><PERSON>ty, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToOne, PrimaryColumn, Relation } from 'typeorm'
import { CustomField } from './custom-field.entity.js'

@Entity()
export class CustomFieldOption {
  @PrimaryColumn({ type: 'varchar' })
  id: string

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'varchar' })
  customFieldId: string

  @ManyToOne(() => CustomField, customField => customField.options)
  @JoinColumn({ name: 'custom_field_id' })
  customField: Relation<CustomField>
}
