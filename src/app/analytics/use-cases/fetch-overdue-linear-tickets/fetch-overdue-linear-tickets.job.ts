import { BaseJob, BaseJobData, PgBossJob } from '@wisemen/pgboss-nestjs-job'
import { QueueName } from '../../../../modules/pgboss/enums/queue-name.enum.js'

export type FetchOverdueLinearTicketsJobData = BaseJobData

@PgBossJob(QueueName.SYSTEM)
export class FetchOverdueLinearTicketsJob extends BaseJob<FetchOverdueLinearTicketsJobData> {
  constructor (data: FetchOverdueLinearTicketsJobData) {
    super(data, {
      singletonKey: 'fetch-overdue-linear-tickets',
      retryDelay: 60
    })
  }
}
