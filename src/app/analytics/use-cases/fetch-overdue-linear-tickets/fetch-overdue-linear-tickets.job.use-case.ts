import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { User } from '@linear/sdk'
import { Person } from '../../entities/person.entity.js'
import { LinearClient } from '../../../../modules/linear/linear.client.js'
import { OverdueLinearTicketsCount } from '../../entities/overdue-linear-tickets.entity.js'

@Injectable()
export class FetchOverdueLinearTicketsJobUseCase {
  constructor (
    private readonly linearClient: LinearClient,
    @InjectRepository(Person)
    private readonly personRepository: Repository<Person>,
    @InjectRepository(OverdueLinearTicketsCount)
    private readonly overdueLinearTicketsRepository: Repository<OverdueLinearTicketsCount>

  ) {}

  public async upsertPersonsLinearId (): Promise<void> {
    const linearUsers = await this.getUsersFromLinear()

    // Get all existing persons from the database
    const existingPersons = await this.personRepository.find()

    // Create a map of existing persons by email for quick lookup
    const personsByEmail = new Map<string, Person>()
    existingPersons.forEach((person) => {
      if (person.email) {
        personsByEmail.set(person.email.toLowerCase(), person)
      }
    })

    // Update persons with matching Linear users
    const personsToUpdate: Person[] = []

    for (const linearUser of linearUsers) {
      if (linearUser.email) {
        const existingPerson = personsByEmail.get(linearUser.email.toLowerCase())

        if (existingPerson && existingPerson.linearId !== linearUser.id) {
          existingPerson.linearId = linearUser.id
          personsToUpdate.push(existingPerson)
        }
      }
    }

    // Batch update all persons that need Linear ID updates
    if (personsToUpdate.length > 0) {
      await this.personRepository.save(personsToUpdate)
      console.log(`Updated ${personsToUpdate.length} persons with Linear IDs`)
    } else {
      console.log('No persons needed Linear ID updates')
    }
  }

  private async getUsersFromLinear (): Promise<User[]> {
    const users: User[] = []
    let hasNextPage = true
    let after: string | undefined = undefined

    while (hasNextPage) {
      const response = await this.linearClient.fetchUsers(100, after)
      users.push(...response.users)
      hasNextPage = response.hasNextPage
      after = response.endCursor
    }

    return users
  }
}
