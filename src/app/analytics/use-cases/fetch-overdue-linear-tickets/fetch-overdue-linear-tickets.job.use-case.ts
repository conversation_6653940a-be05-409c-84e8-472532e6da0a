import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { Person } from '../../entities/person.entity.js'
import { LinearClient } from '../../../../modules/linear/linear.client.js'
import { OverdueLinearTicketsCount } from '../../entities/overdue-linear-tickets.entity.js'
import { User } from '@linear/sdk'

@Injectable()
export class FetchOverdueLinearTicketsJobUseCase {
  constructor (
    private readonly linearClient: LinearClient,
    @InjectRepository(Person)
    private readonly personRepository: Repository<Person>,
    @InjectRepository(OverdueLinearTicketsCount)
    private readonly overdueLinearTicketsRepository: Repository<OverdueLinearTicketsCount>

  ) {}

  public async importPeople (): Promise<void> {
    const peopleResponse = await this.getUsersFromLinear()
  }

  private async getUsersFromLinear (): Promise<User[]> {
    const users: User[] = []
    let hasNextPage = true
    let after: string | undefined = undefined

    while (hasNextPage) {
      const response = await this.linearClient.fetchUsers(100, after)
      users.push(...response.users)
      hasNextPage = response.hasNextPage
      after = response.endCursor
    }

    return users
  }
}
