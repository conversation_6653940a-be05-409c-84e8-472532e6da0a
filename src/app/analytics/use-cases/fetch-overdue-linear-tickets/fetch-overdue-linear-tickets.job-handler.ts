import { Injectable } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON>, PgBossJobHand<PERSON> } from '@wisemen/pgboss-nestjs-job'
import { FetchOverdueLinearTicketsJobUseCase } from './fetch-overdue-linear-tickets.job.use-case.js'
import { FetchOverdueLinearTicketsJob } from './fetch-overdue-linear-tickets.job.js'

@Injectable()
@PgBossJobHandler(FetchOverdueLinearTicketsJob)
export class FetchOverdueLinearTicketsJobHandler extends JobHandler<FetchOverdueLinearTicketsJob> {
  constructor (
    private readonly useCase: FetchOverdueLinearTicketsJobUseCase
  ) {
    super()
  }

  async run (): Promise<void> {
  }
}
