import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { ProductiveModule } from '../../../../modules/productive/productive.module.js'
import { Person } from '../../entities/person.entity.js'
import { OverdueLinearTicketsCount } from '../../entities/overdue-linear-tickets.entity.js'
import { FetchOverdueLinearTicketsJobHandler } from './fetch-overdue-linear-tickets.job-handler.js'
import { FetchOverdueLinearTicketsJobUseCase } from './fetch-overdue-linear-tickets.job.use-case.js'

@Module({
  imports: [
    ProductiveModule,
    TypeOrmModule.forFeature([
      Person, OverdueLinearTicketsCount
    ])
  ],
  providers: [
    FetchOverdueLinearTicketsJobHandler,
    FetchOverdueLinearTicketsJobUseCase
  ]
})
export class FetchAnalyticsJobModule {}
