import { Injectable } from '@nestjs/common'
import { transaction } from '@wisemen/nestjs-typeorm'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { DataSource } from 'typeorm'
import { FetchAnalyticsJob } from '../fetch-analytics/fetch-analytics.job.js'

@Injectable()
export class UpdateAnalyticsUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly scheduler: PgBossScheduler
  ) {}

  async execute (): Promise<void> {
    await transaction(this.dataSource, async () => {
      const job = new FetchAnalyticsJob({})

      await this.scheduler.scheduleJob(job)
    })
  }
}
