import { <PERSON>, <PERSON> } from '@nestjs/common'
import { Public } from '../../../../modules/permission/permission.decorator.js'
import { UpdateAnalyticsUseCase } from './update-analytics.use-case.js'

@Controller('analytics')
export class UpdateAnalyticsController {
  constructor (
    private readonly useCase: UpdateAnalyticsUseCase
  ) {}

  @Post()
  @Public()
  async update (): Promise<void> {
    await this.useCase.execute()
  }
}
