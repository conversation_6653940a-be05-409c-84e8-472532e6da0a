import { Mo<PERSON><PERSON>, OnApplicationBootstrap } from '@nestjs/common'
import { PgBossSchedulerModule } from '@wisemen/pgboss-nestjs-job'
import { Trace } from '@wisemen/opentelemetry'
import { UpdateAnalyticsUseCase } from './update-analytics.use-case.js'
import { UpdateAnalyticsController } from './update-analytics.controller.js'

@Module({
  imports: [
    PgBossSchedulerModule
  ],
  controllers: [
    UpdateAnalyticsController
  ],
  providers: [
    UpdateAnalyticsUseCase
  ],
  exports: [
    UpdateAnalyticsUseCase
  ]
})
export class UpdateAnalyticsModule implements OnApplicationBootstrap {
  constructor (private readonly useCase: UpdateAnalyticsUseCase) {

  }

  @Trace()
  async onApplicationBootstrap (): Promise<void> {
    await this.useCase.execute()
  }
}
