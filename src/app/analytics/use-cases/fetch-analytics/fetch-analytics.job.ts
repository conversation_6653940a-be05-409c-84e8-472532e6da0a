import { BaseJob, BaseJobData, PgBossJob } from '@wisemen/pgboss-nestjs-job'
import { QueueName } from '../../../../modules/pgboss/enums/queue-name.enum.js'

export type FetchAnalyticsJobData = BaseJobData

@PgBossJob(QueueName.SYSTEM)
export class FetchAnalyticsJob extends BaseJob<FetchAnalyticsJobData> {
  constructor (data: FetchAnalyticsJobData) {
    super(data, {
      singletonKey: 'fetch-analytics',
      retryDelay: 60
    })
  }
}
