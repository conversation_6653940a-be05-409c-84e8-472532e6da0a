import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import dayjs from 'dayjs'
import { ProductiveClient } from '../../../../modules/productive/productive.client.js'
import { Person } from '../../entities/person.entity.js'
import { CustomField } from '../../entities/custom-field.entity.js'
import { CustomFieldOption } from '../../entities/custom-field-option.entity.js'
import { PersonCustomField } from '../../entities/person-custom-field.entity.js'
import { PlannedAhead } from '../../entities/planned-ahead.entity.js'
import { PersonTime } from '../../entities/person-time.entity.js'
import { TimeReportAttributes } from '../../../../modules/productive/types/time-reports.type.js'
import { ProductiveData } from '../../../../modules/productive/types/data.type.js'
import { TimeEntryReportAttributes } from '../../../../modules/productive/types/time-entry-reports.type.js'

@Injectable()
export class FetchAnalyticsJobUseCase {
  constructor (
    private readonly productiveClient: ProductiveClient,
    @InjectRepository(Person)
    private readonly personRepository: Repository<Person>,
    @InjectRepository(CustomField)
    private readonly customFieldRepository: Repository<CustomField>,
    @InjectRepository(CustomFieldOption)
    private readonly customFieldOptionRepository: Repository<CustomFieldOption>,
    @InjectRepository(PersonCustomField)
    private readonly personCustomFieldRepository: Repository<PersonCustomField>,
    @InjectRepository(PlannedAhead)
    private readonly plannedAheadRepository: Repository<PlannedAhead>,
    @InjectRepository(PersonTime)
    private readonly personTimeRepository: Repository<PersonTime>
  ) {}

  public async importPeople (): Promise<void> {
    const peopleResponse = await this.productiveClient.fetchPeople()

    const people = peopleResponse.data.map((person) => {
      return this.personRepository.create({
        id: person.id,
        archivedAt: person.attributes.deactivated_at,
        firstName: person.attributes.first_name,
        lastName: person.attributes.last_name,
        email: person.attributes.email
      })
    })

    await this.personRepository.upsert(people, { conflictPaths: { id: true } })

    const personCustomFields = peopleResponse.data.flatMap((person) => {
      if (person.attributes.custom_fields === null) {
        return []
      }

      return Object.entries(person.attributes.custom_fields)
        .flatMap(([customFieldId, customFieldOptionIds]) => {
          if (Array.isArray(customFieldOptionIds) === false) {
            customFieldOptionIds = [customFieldOptionIds]
          }

          return customFieldOptionIds.map((customFieldOptionId) => {
            return this.personCustomFieldRepository.create({
              personId: person.id,
              customFieldId,
              customFieldOptionId
            })
          })
        })
    })

    await this.personCustomFieldRepository.deleteAll()

    await this.personCustomFieldRepository.upsert(personCustomFields, {
      conflictPaths: { personId: true, customFieldId: true, customFieldOptionId: true }
    })
  }

  public async importCustomFields (): Promise<void> {
    await this.personCustomFieldRepository.deleteAll()
    await this.customFieldOptionRepository.deleteAll()
    await this.customFieldRepository.deleteAll()

    const customFields = await this.productiveClient.fetchCustomFields()

    const customFieldsEntities = customFields.data.map((customField) => {
      return this.customFieldRepository.create({
        id: customField.id,
        name: customField.attributes.name,
        dataTypeId: customField.attributes.data_type_id
      })
    })

    await this.customFieldRepository.upsert(customFieldsEntities, { conflictPaths: { id: true } })

    const customFieldOptionsEntities = customFields.data.flatMap((customField) => {
      return customField.relationships.options.data.map((option) => {
        const customFieldOption = customFields.included
          .find(customFieldOption => customFieldOption.id === option.id)

        return this.customFieldOptionRepository.create({
          id: option.id,
          name: customFieldOption?.attributes.name,
          customFieldId: customField.id
        })
      })
    })

    await this.customFieldOptionRepository.upsert(customFieldOptionsEntities, {
      conflictPaths: { id: true }
    })
  }

  public async fetchPlanninAhead (): Promise<void> {
    const timeReports90 = await this.productiveClient.fetchTimeReports({
      group: ['person'],
      until: dayjs().add(90, 'day')
    })

    const timeReports30 = await this.productiveClient.fetchTimeReports({
      group: ['person'],
      until: dayjs().add(30, 'day')
    })

    const plannedAhead = timeReports90.data.map((item) => {
      const item30 = timeReports30.data.find((i) => {
        return i.relationships.person.data.id === item.relationships.person.data.id
      })

      return this.plannedAheadRepository.create({
        personId: item.relationships.person.data.id,
        date: dayjs().format('YYYY-MM-DD'),
        availableTime90Days: item.attributes.available_time,
        scheduledTime90Days: item.attributes.scheduled_time,
        availableTime30Days: item30?.attributes.available_time ?? 0,
        scheduledTime30Days: item30?.attributes.scheduled_time ?? 0
      })
    })

    await this.plannedAheadRepository.upsert(plannedAhead, {
      conflictPaths: { personId: true, date: true }
    })
  }

  public async fetchTimeReports (): Promise<void> {
    const timeReports = await this.fetchAllTimeReports()
    const timeEntryReports = await this.fetchAllTimeEntryReports()
    const supportEntryReports = await this.fetchAllSupportEntryReports()

    type CombinedReports = {
      timeReport?: ProductiveData<TimeReportAttributes>
      timeEntryReport?: ProductiveData<TimeEntryReportAttributes>
      supportEntryReport?: ProductiveData<TimeEntryReportAttributes>
    }

    const people = await this.personRepository.find({
      select: { id: true }
    })

    const personIds = people.map(person => person.id)

    const timeMap = new Map<string, CombinedReports>()

    for (const item of timeReports) {
      const personId = item.relationships.person.data.id
      const week = item.attributes.date_period!

      if (!personIds.includes(personId)) {
        // eslint-disable-next-line no-console
        console.log('Skipping time report for person', personId, week)

        continue
      }

      const key = `${personId}-${week}`

      const entry = timeMap.get(key) ?? {} as CombinedReports

      entry.timeReport = item

      timeMap.set(key, entry)
    }

    for (const item of timeEntryReports) {
      const personId = item.relationships.person.data.id
      const week = item.attributes.date_period!

      const key = `${personId}-${week}`

      const entry = timeMap.get(key) ?? {} as CombinedReports

      entry.timeEntryReport = item

      timeMap.set(key, entry)
    }

    for (const item of supportEntryReports) {
      const personId = item.relationships.person.data.id
      const week = item.attributes.date_period!

      const key = `${personId}-${week}`

      const entry = timeMap.get(key) ?? {} as CombinedReports

      entry.supportEntryReport = item

      timeMap.set(key, entry)
    }

    const personTimeEntities = Array.from(timeMap.values()).map((entry) => {
      const timeReport = entry.timeReport
      const timeEntryReport = entry.timeEntryReport
      const supportEntryReport = entry.supportEntryReport

      if (timeReport == null) {
        // eslint-disable-next-line no-console
        console.log('Missing time report',
          timeEntryReport?.relationships.person.data.id,
          timeEntryReport?.attributes.date_period)

        return null
      }

      return this.personTimeRepository.create({
        personId: timeReport.relationships.person.data.id,
        week: timeReport.attributes.date_period,
        capacity: timeReport.attributes.capacity,
        availableTime: timeReport.attributes.available_time,
        totalTime: timeEntryReport?.attributes.total_time ?? 0,
        totalBillableTime: timeEntryReport?.attributes.total_billable_time ?? 0,
        totalRecognizedTime: timeEntryReport?.attributes.total_recognized_time ?? 0,
        totalSupportTime: supportEntryReport?.attributes.total_time ?? 0,
        totalSupportBillableTime: supportEntryReport?.attributes.total_billable_time ?? 0,
        totalSupportRecognizedTime: supportEntryReport?.attributes.total_recognized_time ?? 0
      })
    }).filter(entry => entry != null)

    await this.personTimeRepository.upsert(personTimeEntities, {
      conflictPaths: { personId: true, week: true }
    })
  }

  private async fetchAllTimeReports (): Promise<ProductiveData<TimeReportAttributes>[]> {
    const lastWeek = dayjs().set('day', 0)
    const start = dayjs().set('day', 1).subtract(8, 'week')

    const timeReports: ProductiveData<TimeReportAttributes>[] = []

    let page = 1
    let hasNextPage = true

    while (hasNextPage) {
      // eslint-disable-next-line no-console
      console.log('Fetching page', page)

      const response = await this.productiveClient.fetchTimeReports({
        group: ['date:week', 'person'],
        from: start,
        until: lastWeek,
        page
      })

      timeReports.push(...response.data)

      hasNextPage = response.links.next != null

      page++
    }

    return timeReports
  }

  private async fetchAllTimeEntryReports (): Promise<ProductiveData<TimeEntryReportAttributes>[]> {
    const lastWeek = dayjs().set('day', 0)
    const start = dayjs().set('day', 1).subtract(8, 'week')

    const timeEntryReports: ProductiveData<TimeEntryReportAttributes>[] = []

    let page = 1
    let hasNextPage = true

    while (hasNextPage) {
      // eslint-disable-next-line no-console
      console.log('Fetching page', page)

      const response = await this.productiveClient.fetchTimeEntryReports({
        group: ['date:week', 'person'],
        from: start,
        until: lastWeek,
        page
      })

      timeEntryReports.push(...response.data)

      hasNextPage = response.links.next != null

      page++
    }

    return timeEntryReports
  }

  private async fetchAllSupportEntryReports (

  ): Promise<ProductiveData<TimeEntryReportAttributes>[]> {
    const lastWeek = dayjs().set('day', 0)
    const start = dayjs().set('day', 1).subtract(8, 'week')

    const timeEntryReports: ProductiveData<TimeEntryReportAttributes>[] = []

    let page = 1
    let hasNextPage = true

    while (hasNextPage) {
      // eslint-disable-next-line no-console
      console.log('Fetching page', page)

      const response = await this.productiveClient.fetchSupportEntryReports({
        group: ['date:week', 'person'],
        from: start,
        until: lastWeek,
        page
      })

      timeEntryReports.push(...response.data)

      hasNextPage = response.links.next != null

      page++
    }

    return timeEntryReports
  }
}
