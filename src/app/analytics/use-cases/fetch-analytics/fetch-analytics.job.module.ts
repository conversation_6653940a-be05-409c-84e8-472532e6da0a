import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { ProductiveModule } from '../../../../modules/productive/productive.module.js'
import { Person } from '../../entities/person.entity.js'
import { CustomField } from '../../entities/custom-field.entity.js'
import { CustomFieldOption } from '../../entities/custom-field-option.entity.js'
import { PersonCustomField } from '../../entities/person-custom-field.entity.js'
import { PlannedAhead } from '../../entities/planned-ahead.entity.js'
import { PersonTime } from '../../entities/person-time.entity.js'
import { FetchAnalyticsJobHandler } from './fetch-anayltics.job-handler.js'
import { FetchAnalyticsJobUseCase } from './fetch-analytics.job.use-case.js'

@Module({
  imports: [
    ProductiveModule,
    TypeOrmModule.forFeature([
      Person, CustomField, CustomFieldOption, PersonCustomField, PlannedAhead, PersonTime
    ])
  ],
  providers: [
    FetchAnalyticsJobHandler,
    FetchAnalyticsJobUseCase
  ]
})
export class FetchAnalyticsJobModule {}
