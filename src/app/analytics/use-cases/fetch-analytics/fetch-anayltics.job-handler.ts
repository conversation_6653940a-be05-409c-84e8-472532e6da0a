import { Injectable } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON>, PgBossJob<PERSON><PERSON><PERSON> } from '@wisemen/pgboss-nestjs-job'
import { FetchAnalyticsJobUseCase } from './fetch-analytics.job.use-case.js'
import { FetchAnalyticsJob } from './fetch-analytics.job.js'

@Injectable()
@PgBossJobHandler(FetchAnalyticsJob)
export class FetchAnalyticsJobHandler extends JobHandler<FetchAnalyticsJob> {
  constructor (
    private readonly useCase: FetchAnalyticsJobUseCase
  ) {
    super()
  }

  async run (): Promise<void> {
    await this.useCase.importCustomFields()
    await this.useCase.importPeople()
    await this.useCase.fetchPlanninAhead()
    await this.useCase.fetchTimeReports()
  }
}
