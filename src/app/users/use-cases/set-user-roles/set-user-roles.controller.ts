import { <PERSON>, Controller, Post } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { UserUuid } from '../../entities/user.uuid.js'
import { SetUserRolesUseCase } from './set-user-roles.use-case.js'
import { SetUserRolesCommand } from './set-user-roles.command.js'

@ApiTags('User')
@ApiOAuth2([])
@Controller('users/:user/role')
export class SetUserRolesController {
  constructor (
    private readonly useCase: SetUserRolesUseCase
  ) {}

  @Post()
  @Permissions(Permission.USER_UPDATE)
  async updateUser (
    @UuidParam('user') userUuid: UserUuid,
    @Body() dto: SetUserRolesCommand
  ): Promise<void> {
    await this.useCase.changeRoles(userUuid, dto)
  }
}
