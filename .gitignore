# Compiled output
dist
node_modules
/typesense-data
.typesense/
.postgres
.minio
.machinekey
_build

# Logs
logs
*.log
lerna-debug.log*
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# Tests
/coverage
/.nyc_output

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/tasks.json
!.vscode/extensions.json
.c9/
.classpath
.fleet
.cursor/
.idea
.project
.settings/
.DS_Store
*.launch
*.suo
*.ntvs*
*.njsproj
*.sln
*.sublime-workspace
*.sw?
# Local env files
.env
.env.*
!.env.test
!.env.example

# Certificates
*.pem

# Caches
.eslintcache
