TZ=UTC
NODE_ENV=test

FRONTEND_URL=https://template-project.development.appwi.se
BACKEND_URL=

DATABASE_SSL=false
DATABASE_URI=postgresql://postgres:password@localhost:5432/test_db
DATABASE_REPLICA_URIS=

REDIS_URL=redis://localhost:6379

TYPESENSE_HOST=127.0.0.1
TYPESENSE_KEY=api_key

NATS_HOST=127.0.0.1
NATS_PORT=4222

S3_ENDPOINT=http://localhost:9000
# S3_REGION=
S3_ACCESS_KEY=admin
S3_SECRET_KEY=password
S3_BUCKET=test-bucket

MAIL_FROM_NAME=
SCW_PROJECT_ID=
SCW_REGION=
SCW_API_KEY=

SENTRY_DSN=
SENTRY_TRACE_SAMPLE_RATE=
SENTRY_ERROR_SAMPLE_RATE=

SIGNOZ_LOG_ENDPOINT=
SIGNOZ_TRACE_ENDPOINT=
SIGNOZ_INGESTION_KEY=
SIGNOZ_AUTH=
SIGNOZ_KEY=

AUTH_JWKS_ENDPOINT=https://zitadel.internal.appwi.se/oauth/v2/keys
AUTH_ISSUER=https://zitadel.internal.appwi.se
AUTH_PROJECT_ID=284257737964130471

OPEN_API_SCOPES=urn:zitadel:iam:org:id:305531015504349194 users
OPEN_API_SERVERS=https://vue.project-template.development.appwi.se,https://vue.project-template.test.appwi.se,https://vue.project-template.staging.appwi.se,https://vue.project-template.production.appwi.se
OPEN_API_OPENID_CONFIGURATION_URL=https://zitadel.internal.appwi.se/.well-known/openid-configuration

TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=

ONESIGNAL_API_KEY=
ONESIGNAL_APP_ID=
ONESIGNAL_PRIVATE_KEY=
ONESIGNAL_PASSPHRASE=
ONESIGNAL_PUBLIC_KEY=

ZITADEL_BASE_URL=https://zitadel.internal.appwi.se
ZITADEL_ORGANISATION_ID=
ZITADEL_API_TOKEN=

PRODUCTIVE_API_KEY=
PRODUCTIVE_ORG_ID=
